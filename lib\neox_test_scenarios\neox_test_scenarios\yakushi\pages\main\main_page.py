#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Page Object

This module provides the Page Object implementation for Yakushi main page functionality.
It encapsulates all main page UI elements and operations.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   main_page.py
@Software   :   PyCharm
"""

import time
from typing import Any, Dict, Optional

import allure
import uiautomation as ui
from neox_test_common import UIA, logger

from ..base.base_page import BasePage


class MainPage(BasePage):
    """
    Yakushi主页面的Page Object类

    该类封装了主页面的所有UI元素和操作，包括浮动窗口、主窗口、
    导航按钮等功能。提供了主页面相关的操作方法。

    继承自BasePage，具有基础页面的所有通用功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化主页面

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        super().__init__(config)

    def get_main_window(self) -> Optional[ui.WindowControl]:
        """
        获取主窗口控件

        Returns:
            WindowControl: 主窗口控件，如果未找到则返回None
        """
        main_window_auto_id = self.get_element_config(
            "yakushi.modules.common.window.main.auto_id"
        )
        return self.find_window(auto_id=main_window_auto_id)

    def get_float_window(self) -> Optional[ui.WindowControl]:
        """
        获取浮动窗口控件

        Returns:
            WindowControl: 浮动窗口控件，如果未找到则返回None
        """
        float_window_auto_id = self.get_element_config(
            "yakushi.modules.common.window.float.auto_id"
        )
        return self.find_window(auto_id=float_window_auto_id)

    def get_main_button(self) -> Optional[ui.ButtonControl]:
        """
        获取主窗口显示按钮

        Returns:
            ButtonControl: 主窗口显示按钮，如果未找到则返回None
        """
        float_window = self.get_float_window()
        if not float_window:
            return None

        _ = self.get_element_config("yakushi.modules.common.button.main.auto_id")
        return self.find_element_by_config(
            "yakushi.modules.common.button.main",
            parent=float_window,
            element_type="Button",
        )

    def get_logo_button(self) -> Optional[ui.ButtonControl]:
        """
        获取Logo按钮

        Returns:
            ButtonControl: Logo按钮，如果未找到则返回None
        """
        float_window = self.get_float_window()
        if not float_window:
            return None

        return self.find_element_by_config(
            "yakushi.modules.common.button.logo",
            parent=float_window,
            element_type="Button",
        )

    def open_main_window_from_float(self) -> bool:
        """
        从浮动窗口打开主窗口

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("从浮动窗口打开主窗口"):
                float_window = self.get_float_window()
                if not float_window:
                    logger.error("Float window not found")
                    return False

                self.activate_window(float_window)

                main_button = self.get_main_button()
                if not main_button:
                    logger.error("Main button not found in float window")
                    return False

                success = UIA.clickButton(button=main_button)
                if success:
                    logger.info("Successfully clicked main window button")
                    self.log_element_info(main_button, "主窗口按钮")

                    # Wait for main window to appear
                    time.sleep(2)
                    main_window = self.get_main_window()
                    if main_window:
                        logger.info("Main window opened successfully")
                        self.log_element_info(main_window, "主窗口")
                        return True
                    else:
                        logger.error("Main window did not appear after clicking button")
                        return False
                else:
                    logger.error("Failed to click main window button")
                    return False

        except Exception as e:
            logger.error(f"Error opening main window from float: {e}")
            return False

    def verify_main_page_loaded(self) -> bool:
        """
        验证主页面是否已加载

        Returns:
            bool: 主页面是否已成功加载
        """
        try:
            with allure.step("验证主页面已加载"):
                # Check for both main window and float window
                main_window = self.get_main_window()
                float_window = self.get_float_window()

                if main_window and float_window:
                    logger.info("Main page loaded successfully - Both windows found")
                    self.log_element_info(main_window, "主窗口")
                    self.log_element_info(float_window, "浮动窗口")
                    return True
                elif float_window:
                    logger.info("Main page partially loaded - Float window found")
                    self.log_element_info(float_window, "浮动窗口")
                    return True
                else:
                    logger.error("Main page not loaded - No windows found")
                    return False

        except Exception as e:
            logger.error(f"Error verifying main page loaded: {e}")
            return False

    def navigate_to_prescription_history(self) -> bool:
        """
        导航到处方笺履历页面

        Returns:
            bool: 导航是否成功
        """
        # This method will be implemented based on specific navigation requirements
        # Placeholder for now
        logger.info("Navigating to prescription history page...")
        return True

    def navigate_to_nsips_comparison(self) -> bool:
        """
        导航到NSIPS突合页面

        Returns:
            bool: 导航是否成功
        """
        # This method will be implemented based on specific navigation requirements
        # Placeholder for now
        logger.info("Navigating to NSIPS comparison page...")
        return True

    def navigate_to_settings(self) -> bool:
        """
        导航到设定页面

        Returns:
            bool: 导航是否成功
        """
        # This method will be implemented based on specific navigation requirements
        # Placeholder for now
        logger.info("Navigating to settings page...")
        return True

    def toggle_window_size(self) -> bool:
        """
        切换窗口大小

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("切换主窗口大小"):
                main_window = self.get_main_window()
                if not main_window:
                    logger.error("Main window not found for size toggle")
                    return False

                # Implementation will depend on specific UI controls for window size toggle
                # This is a placeholder for the actual implementation
                logger.info("Window size toggle functionality - to be implemented")
                return True

        except Exception as e:
            logger.error(f"Error toggling window size: {e}")
            return False
