# KENTA 项目设置指南

## 项目结构调整说明

此项目的 `pyproject.toml` 已从根目录移动到具体项目目录中，以实现更好的模块化管理。

## 设置步骤

### 1. 进入项目目录
```bash
cd C:\Bitbucket\testing\Automation\Windows\KENTA
```

### 2. 创建虚拟环境
```bash
uv venv
```

### 3. 激活虚拟环境
```bash
# Windows PowerShell
.venv\Scripts\Activate.ps1

# Windows CMD
.venv\Scripts\activate.bat
```

### 4. 安装依赖
```bash
uv sync
```

### 5. 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试套件
pytest testsuite/登录登出/

# 生成 HTML 报告
pytest --html=report/report.html
```

## 主要变化

1. **项目配置独立化**: 每个项目现在有自己的 `pyproject.toml`
2. **路径调整**: 本地包路径已调整为相对于当前项目目录的正确路径
3. **测试路径**: pytest 配置中的 `testpaths` 已调整为 `testsuite`
4. **虚拟环境**: 可以在项目目录下创建独立的虚拟环境

## 依赖说明

### 测试框架依赖
- pytest 及相关插件
- allure-pytest (测试报告)

### Windows UI 自动化依赖 (KENTA 特定)
- playwright (Web 自动化)
- PyAutoGUI (桌面自动化)
- uiautomation (Windows UI 自动化)
- pillow (图像处理)

### 本地共享库
- neox_test_common (通用测试工具)
- neox_test_win (Windows 特定工具)
- neox_test_scenarios (测试场景)

## 后续扩展

如果需要添加其他项目（如其他平台的自动化测试），可以在相应目录下创建类似的 `pyproject.toml` 配置文件。
