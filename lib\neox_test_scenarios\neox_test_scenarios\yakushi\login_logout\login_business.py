# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   login_business.py
@Software   :   PyCharm
"""

from .. import LoginPage


def perform_login_flow(config: dict, use_correct_credentials: bool = True) -> None:
    """
    使用 POM 模式执行完整的登录流程

    Args:
        config (dict): 测试配置数据
        use_correct_credentials (bool): True使用正确凭据，False使用错误凭据
    """
    login_page = LoginPage(config)
    login_page.perform_complete_login_flow(use_correct_credentials)


def verify_login_success(config: dict) -> None:
    """
    使用 POM 模式验证登录成功
    """
    login_page = LoginPage(config)
    login_page.verify_login_success()


def verify_login_failure() -> None:
    """
    使用 POM 模式验证登录失败
    """
    # 创建一个空的配置字典，因为验证失败不需要配置
    login_page = LoginPage({})
    login_page.verify_login_failure()
