#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NeoX Test Windows Library

This package provides Windows-specific testing utilities and configurations
for the NeoX testing framework. It includes screen management, DPI handling,
and application-specific configuration for Windows-based testing scenarios.

The library is particularly focused on Yakushi application testing on Windows
platforms, providing feature file paths, screen resolution management, and
Windows-specific automation utilities.

@Date       :   2024-02-11
<AUTHOR>   Kuno<PERSON>u
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import Yakushi-specific Windows utilities
from .yakushi import FEATURES_NAME

# Try to import Screen class if available
try:
    from .yakushi import Screen

    _screen_available = True
except ImportError:
    _screen_available = False

# Define public API
__all__ = [
    # Configuration constants
    "FEATURES_NAME",  # Yakushi feature file paths configuration
]

# Add Screen to __all__ if available
if _screen_available:
    __all__.append("Screen")
