#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base Page Class

This module provides the base page class that contains common functionality for all Page Object
implementations in the Yakushi testing framework.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   base_page.py
@Software   :   PyCharm
"""

import functools
import time
from typing import Any, Dict, Optional

import uiautomation as ui
from jmespath import search
from neox_test_common import UIA, logger


def retry(max_attempts: int = 3, delay: float = 1.0):
    """
    重试装饰器，用于提高UI操作的稳定性

    Args:
        max_attempts (int): 最大重试次数
        delay (float): 重试间隔时间（秒）

    Returns:
        装饰器函数
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"操作失败，已重试{max_attempts}次：{e}")
                        raise
                    logger.warning(f"操作失败，第{attempt + 1}次重试：{e}")
                    time.sleep(delay)
            return None

        return wrapper

    return decorator


class BasePage:
    """
    Yakushi应用程序的基础页面类

    该类提供了所有Page Object类的通用功能，包括配置管理、元素定位、
    窗口操作等基础方法。所有具体的页面类都应该继承此基础类。

    Attributes:
        config (dict): 测试配置数据，包含yakushi.toml的配置信息
        window_name (str): Yakushi应用程序的窗口名称
        window_class (str): Yakushi应用程序的窗口类名
    """

    # Constants for Yakushi application
    YAKUSHI_WINDOW_NAME = "薬師丸賢太"
    YAKUSHI_WINDOW_CLASS = "Window"
    DEFAULT_SEARCH_DEPTH = 1
    DEFAULT_TIMEOUT = 5

    def __init__(self, config: Dict[str, Any]):
        """
        初始化基础页面类

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        self.config = config
        self.window_name = self.YAKUSHI_WINDOW_NAME
        self.window_class = self.YAKUSHI_WINDOW_CLASS

    def get_element_config(self, config_path: str) -> Any:
        """
        从配置文件获取元素定位信息

        Args:
            config_path (str): 配置路径，使用jmespath语法

        Returns:
            Any: 配置值，如果路径不存在则返回None

        Example:
            >>> page = BasePage(config)
            >>> user_box_config = page.get_element_config("yakushi.modules.login.control.box.user")
        """
        try:
            return search(config_path, self.config)
        except Exception as e:
            logger.error(f"Failed to get config for path '{config_path}': {e}")
            return None

    def find_window(
        self,
        auto_id: Optional[str] = None,
        search_depth: int = DEFAULT_SEARCH_DEPTH,
        timeout: int = DEFAULT_TIMEOUT,
    ) -> Optional[ui.WindowControl]:
        """
        查找Yakushi应用程序窗口

        Args:
            auto_id (str, optional): 窗口的AutomationId
            search_depth (int): 搜索深度
            timeout (int): 超时时间（秒）

        Returns:
            WindowControl: 窗口控件对象，如果未找到则返回None
        """
        try:
            return UIA.WindowControl(
                Name=self.window_name,
                ClassName=self.window_class,
                AutomationId=auto_id,
                searchDepth=search_depth,
                timeout=timeout,
            )
        except Exception as e:
            logger.error(f"Failed to find window: {e}")
            return None

    def find_window_by_args(
        self,
        window_name: str = "",
        class_name: str = "",
        search_depth: int = DEFAULT_SEARCH_DEPTH,
        timeout: int = DEFAULT_TIMEOUT,
    ) -> Optional[ui.WindowControl]:
        """
        根据窗口参数查找窗口

        Args:
            window_name (str): 窗口名称
            class_name (str): 窗口类名
            search_depth (int): 搜索深度
            timeout (int): 超时时间（秒）

        Returns:
            WindowControl: 窗口控件对象，如果未找到则返回None
        """
        try:
            return UIA.WindowControl(
                Name=window_name,
                ClassName=class_name,
                searchDepth=search_depth,
                timeout=timeout,
            )
        except Exception as e:
            logger.error(f"Failed to find window: {e}")
            return None

    def find_element_by_config(
        self,
        config_path: str,
        parent: Optional[ui.Control] = None,
        element_type: str = "Control",
        **kwargs,
    ) -> Optional[ui.Control]:
        """
        根据配置路径查找UI元素

        Args:
            config_path (str): 配置路径，使用jmespath语法
            parent (Control, optional): 父控件，如果为None则使用主窗口
            element_type (str): 元素类型（Button, TextBox, etc.）
            **kwargs: 额外的查找参数

        Returns:
            Control: UI控件对象，如果未找到则返回None
        """
        element_config = self.get_element_config(config_path)
        if not element_config:
            logger.error(f"No config found for path: {config_path}")
            return None

        if parent is None:
            parent = self.find_window()
            if not parent:
                logger.error("Failed to find parent window")
                return None

        try:
            # Build search properties from config
            search_properties = {}
            if isinstance(element_config, dict):
                if "auto_id" in element_config:
                    search_properties["AutomationId"] = element_config["auto_id"]
                if "class_name" in element_config:
                    search_properties["ClassName"] = element_config["class_name"]
                if "name" in element_config:
                    search_properties["Name"] = element_config["name"]
                if "btn_name" in element_config:
                    search_properties["Name"] = element_config["btn_name"]

            # Merge with additional kwargs
            search_properties.update(kwargs)

            # Get the appropriate control method
            control_method = getattr(parent, f"{element_type}Control", None)
            if not control_method:
                logger.error(f"Invalid element type: {element_type}")
                return None

            return control_method(**search_properties)

        except Exception as e:
            logger.error(f"Failed to find element for config path '{config_path}': {e}")
            return None

    def activate_window(self, window: Optional[ui.WindowControl] = None) -> bool:
        """
        激活窗口

        Args:
            window (WindowControl, optional): 要激活的窗口，如果为None则使用主窗口

        Returns:
            bool: 操作是否成功
        """
        if window is None:
            window = self.find_window()

        if window:
            try:
                UIA.setWindowActive(window)
                return True
            except Exception as e:
                logger.error(f"Failed to activate window: {e}")
                return False
        return False

    @retry(max_attempts=3, delay=1.0)
    def find_element_with_retry(
        self,
        config_path: str,
        parent: Optional[ui.Control] = None,
        element_type: str = "Control",
        **kwargs,
    ) -> Optional[ui.Control]:
        """
        带重试机制的元素查找方法

        Args:
            config_path (str): 配置路径，使用jmespath语法
            parent (Control, optional): 父控件，如果为None则使用主窗口
            element_type (str): 元素类型（Button, TextBox, etc.）
            **kwargs: 额外的查找参数

        Returns:
            Control: UI控件对象，如果未找到则返回None
        """
        element = self.find_element_by_config(
            config_path, parent, element_type, **kwargs
        )
        if element is None:
            raise Exception(f"Element not found for config path: {config_path}")
        return element

    def log_element_info(self, element: ui.Control, description: str) -> None:
        """
        记录元素信息到日志

        Args:
            element (Control): UI控件对象
            description (str): 元素描述
        """
        if element:
            try:
                rect = element.BoundingRectangle
                logger.info(f"{description}-RECT：[ {rect} ]")
            except Exception as e:
                logger.warning(
                    f"Failed to get element rectangle for {description}: {e}"
                )
        else:
            logger.warning(f"Element is None for {description}")
