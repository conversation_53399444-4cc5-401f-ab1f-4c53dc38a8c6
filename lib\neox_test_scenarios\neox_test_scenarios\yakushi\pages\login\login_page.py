#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login Page Object

This module provides the Page Object implementation for Yakushi login functionality.
It encapsulates all login-related UI elements and operations.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   login_page.py
@Software   :   PyCharm
"""

import random
import string
import time
from typing import Any, Dict, Optional

import allure
import uiautomation as ui
from neox_test_common import logger

from ...common import (
    com_click_confirm_btn,
    com_click_login_btn,
    com_open_yakushi_app,
    com_show_desktop,
    com_write_acc_info,
)
from ..base.base_page import BasePage


class LoginPage(BasePage):
    """
    Yakushi登录页面的Page Object类

    该类封装了登录页面的所有UI元素和操作，包括用户名输入、密码输入、
    登录按钮点击等功能。提供了完整的登录流程操作方法。

    继承自BasePage，具有基础页面的所有通用功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化登录页面

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        super().__init__(config)

    def get_username_textbox(self) -> Optional[ui.EditControl]:
        """
        获取用户名输入框控件

        Returns:
            EditControl: 用户名输入框控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.box.user", element_type="Edit"
        )

    def get_password_textbox(self) -> Optional[ui.EditControl]:
        """
        获取密码输入框控件

        Returns:
            EditControl: 密码输入框控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.box.password", element_type="Edit"
        )

    def get_login_button(self) -> Optional[ui.ButtonControl]:
        """
        获取登录按钮控件

        Returns:
            ButtonControl: 登录按钮控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.btn.login", element_type="Button"
        )

    def input_username(self, username: str) -> bool:
        """
        在用户名输入框中输入用户名

        Args:
            username (str): 要输入的用户名

        Returns:
            bool: 操作是否成功
        """
        user_data = {
            "control": self.get_element_config(
                "yakushi.modules.login.control.box.user"
            ),
            "log": {
                "debug": "账户信息文本框窗口",
                "info": "< Step :: 在账户信息文本框中输入用户名 >",
            },
            "text": username,
        }

        return com_write_acc_info(user_data)

    def input_password(self, password: str) -> bool:
        """
        在密码输入框中输入密码

        使用已有的 com_write_acc_info 函数来避免重复实现

        Args:
            password (str): 要输入的密码

        Returns:
            bool: 操作是否成功
        """
        pwd_data = {
            "control": self.get_element_config(
                "yakushi.modules.login.control.box.password"
            ),
            "log": {
                "debug": "账户密码文本框窗口",
                "info": "< Step :: 在账户密码文本框中输入密码 >",
            },
            "text": password,
        }

        return com_write_acc_info(pwd_data)

    def click_login_button(self) -> bool:
        """
        点击登录按钮

        Returns:
            bool: 操作是否成功
        """
        # 构建 com_click_login_btn 需要的数据结构
        btn_data = {
            "control": self.get_element_config(
                "yakushi.modules.login.control.btn.login"
            ),
            "log": {"debug": "登录按钮", "info": "< Step :: 点击登录按钮 >"},
        }

        return com_click_login_btn(btn_data)

    def click_confirm_button(self, parent_window: ui.WindowControl) -> bool:
        """
        点击确认按钮

        Returns:
            bool: 操作是否成功
        """
        return com_click_confirm_btn(parent_window=parent_window)

    def open_yakushi_app(self) -> Optional[ui.WindowControl]:
        """
        打开Yakushi客户端应用程序

        Returns:
            WindowControl: 登录窗口控件，如果打开失败则返回None
        """
        # 使用已有的 common 函数
        return com_open_yakushi_app(self.config)

    def wait_for_login_result(self, timeout: int = 10) -> bool:
        """
        等待登录结果

        Args:
            timeout (int): 等待超时时间（秒）

        Returns:
            bool: True表示登录成功，False表示登录失败或超时
        """
        # Implementation will be added based on specific login result detection logic
        # This is a placeholder for now
        time.sleep(timeout)  # Basic wait for login processing
        return True

    def perform_complete_login_flow(self, use_correct_credentials: bool = True) -> None:
        """
        执行完整的登录流程

        Args:
            use_correct_credentials (bool): True使用正确凭据，False使用错误凭据
        """
        # Step 1: Show desktop
        with allure.step("显示桌面"):
            com_show_desktop()

        # Step 2: Open Yakushi client
        with allure.step("打开Yakushi客户端"):
            window = self.open_yakushi_app()
            assert window is not None, "Failed to open Yakushi client"

        # Step 3: Input username
        if use_correct_credentials:
            username = self.get_element_config("yakushi.modules.login.account")
            step_desc = "在账户信息文本框中输入正确的账户名"
        else:
            username = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(8)
            )
            step_desc = "在账户信息文本框中输入错误的账户名"

        with allure.step(step_desc):
            success = self.input_username(username)
            assert success, f"Failed to input username: {username}"

        # Step 4: Input password
        if use_correct_credentials:
            password = self.get_element_config("yakushi.modules.login.password")
            step_desc = "在密码文本框中输入正确的密码"
        else:
            password = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(8)
            )
            step_desc = "在密码文本框中输入错误的密码"

        with allure.step(step_desc):
            success = self.input_password(password)
            assert success, "Failed to input password"

        # Step 5: Click login button
        with allure.step("点击登录按钮"):
            success = self.click_login_button()
            assert success, "Failed to click login button"

    def verify_login_success(self) -> None:
        """
        验证登录成功

        检查是否成功跳转到主页面，验证登录操作是否成功完成
        """
        with allure.step("验证登录成功"):
            # Wait for potential login processing
            time.sleep(3)

            # Check for main window or float window
            main_window_auto_id = self.get_element_config(
                "yakushi.modules.common.window.main.auto_id"
            )
            float_window_auto_id = self.get_element_config(
                "yakushi.modules.common.window.float.auto_id"
            )

            main_window = self.find_window(auto_id=main_window_auto_id, timeout=5)
            float_window = self.find_window(auto_id=float_window_auto_id, timeout=5)

            if main_window:
                logger.info("Login successful - Main window found")
                self.log_element_info(main_window, "主窗口")
            else:
                logger.error("Main window not found")

            if float_window:
                logger.info("Login successful - Float window found")
                self.log_element_info(float_window, "浮动窗口")
            else:
                logger.error("Float window not found")

            success = all([main_window, float_window])
            assert success, "Login failed - Neither main window nor float window found"

    def verify_login_failure(self) -> None:
        """
        验证登录失败

        检查登录失败后的状态，确认仍然停留在登录页面
        """
        with allure.step("验证登录失败"):
            time.sleep(2)

            login_failed_confirm_window = self.find_window_by_args(
                window_name="", class_name="#32770", search_depth=2, timeout=5
            )

            if login_failed_confirm_window:
                logger.info(
                    "Login failed as expected - Located login failure confirmation window"
                )
                self.log_element_info(login_failed_confirm_window, "登录失败确认窗口")
            else:
                logger.error("Login failure confirmation window not found")

            success = self.click_confirm_button(
                parent_window=login_failed_confirm_window
            )
            assert success, (
                "Failed to click login failed confirm button. Expected login failure but login window not found"
            )
