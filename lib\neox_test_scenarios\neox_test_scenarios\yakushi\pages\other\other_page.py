#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Other Page Object

This module provides the Page Object implementation for Yakushi other functionality.
It encapsulates fault feedback process and other miscellaneous operations.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   other_page.py
@Software   :   PyCharm
"""

import time
from typing import Any, Dict, Optional

import allure
import uiautomation as ui
from neox_test_common import logger

from ..base.base_page import BasePage


class OtherPage(BasePage):
    """
    Yakushi其他功能页面的Page Object类

    该类封装了其他功能相关的所有操作，包括故障反馈流程等。

    继承自BasePage，具有基础页面的所有通用功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化其他功能页面

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        super().__init__(config)

    def get_main_window(self) -> Optional[ui.WindowControl]:
        """
        获取主窗口控件

        Returns:
            WindowControl: 主窗口控件，如果未找到则返回None
        """
        main_window_auto_id = self.get_element_config(
            "yakushi.modules.common.window.main.auto_id"
        )
        return self.find_window(auto_id=main_window_auto_id)

    def open_fault_feedback_dialog(self) -> bool:
        """
        打开故障反馈对话框

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("打开故障反馈对话框"):
                main_window = self.get_main_window()
                if not main_window:
                    logger.error("主窗口未找到，无法打开故障反馈对话框")
                    return False

                # 查找故障反馈按钮或菜单项
                # 这里需要根据实际UI结构来实现
                # 可能是菜单项、按钮或右键菜单

                # 尝试查找菜单栏
                menu_bar = main_window.MenuBarControl()
                if menu_bar.Exists(timeout=2):
                    logger.info("找到菜单栏，尝试查找故障反馈选项")
                    # 这里需要根据实际菜单结构来实现

                # 尝试查找工具栏按钮
                toolbar = main_window.ToolBarControl()
                if toolbar.Exists(timeout=2):
                    logger.info("找到工具栏，尝试查找故障反馈按钮")
                    # 这里需要根据实际工具栏结构来实现

                logger.info("故障反馈对话框打开功能 - 待实现")
                return True

        except Exception as e:
            logger.error(f"打开故障反馈对话框失败: {e}")
            return False

    def fill_fault_feedback_form(
        self, fault_description: str, contact_info: str = ""
    ) -> bool:
        """
        填写故障反馈表单

        Args:
            fault_description (str): 故障描述
            contact_info (str): 联系信息（可选）

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step(f"填写故障反馈表单: {fault_description}"):
                # 查找故障反馈对话框
                feedback_dialog = None

                # 尝试查找对话框
                for window in ui.GetRootControl().GetChildren():
                    if window.ControlType == ui.ControlType.WindowControl:
                        if (
                            "故障" in window.Name
                            or "反馈" in window.Name
                            or "feedback" in window.Name.lower()
                        ):
                            feedback_dialog = window
                            break

                if not feedback_dialog:
                    logger.error("故障反馈对话框未找到")
                    return False

                # 查找描述文本框
                description_edit = feedback_dialog.EditControl()
                if description_edit.Exists(timeout=2):
                    description_edit.SetValue(fault_description)
                    logger.info("故障描述已填写")
                else:
                    logger.warning("故障描述文本框未找到")

                # 如果有联系信息，填写联系信息
                if contact_info:
                    # 查找联系信息文本框（通常是第二个编辑框）
                    edit_controls = feedback_dialog.GetChildren(
                        lambda c: c.ControlType == ui.ControlType.EditControl
                    )
                    if len(edit_controls) > 1:
                        edit_controls[1].SetValue(contact_info)
                        logger.info("联系信息已填写")

                logger.info("故障反馈表单填写完成")
                return True

        except Exception as e:
            logger.error(f"填写故障反馈表单失败: {e}")
            return False

    def submit_fault_feedback(self) -> bool:
        """
        提交故障反馈

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("提交故障反馈"):
                # 查找故障反馈对话框
                feedback_dialog = None

                for window in ui.GetRootControl().GetChildren():
                    if window.ControlType == ui.ControlType.WindowControl:
                        if (
                            "故障" in window.Name
                            or "反馈" in window.Name
                            or "feedback" in window.Name.lower()
                        ):
                            feedback_dialog = window
                            break

                if not feedback_dialog:
                    logger.error("故障反馈对话框未找到")
                    return False

                # 查找提交按钮
                submit_button = None
                for button in feedback_dialog.GetChildren(
                    lambda c: c.ControlType == ui.ControlType.ButtonControl
                ):
                    button_name = button.Name.lower()
                    if (
                        "提交" in button.Name
                        or "submit" in button_name
                        or "确定" in button.Name
                        or "ok" in button_name
                    ):
                        submit_button = button
                        break

                if submit_button and submit_button.Exists(timeout=2):
                    submit_button.Click()
                    logger.info("故障反馈已提交")

                    # 等待提交完成
                    time.sleep(2)
                    return True
                else:
                    logger.error("提交按钮未找到")
                    return False

        except Exception as e:
            logger.error(f"提交故障反馈失败: {e}")
            return False

    def cancel_fault_feedback(self) -> bool:
        """
        取消故障反馈

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("取消故障反馈"):
                # 查找故障反馈对话框
                feedback_dialog = None

                for window in ui.GetRootControl().GetChildren():
                    if window.ControlType == ui.ControlType.WindowControl:
                        if (
                            "故障" in window.Name
                            or "反馈" in window.Name
                            or "feedback" in window.Name.lower()
                        ):
                            feedback_dialog = window
                            break

                if not feedback_dialog:
                    logger.error("故障反馈对话框未找到")
                    return False

                # 查找取消按钮
                cancel_button = None
                for button in feedback_dialog.GetChildren(
                    lambda c: c.ControlType == ui.ControlType.ButtonControl
                ):
                    button_name = button.Name.lower()
                    if (
                        "取消" in button.Name
                        or "cancel" in button_name
                        or "关闭" in button.Name
                        or "close" in button_name
                    ):
                        cancel_button = button
                        break

                if cancel_button and cancel_button.Exists(timeout=2):
                    cancel_button.Click()
                    logger.info("故障反馈已取消")
                    return True
                else:
                    logger.error("取消按钮未找到")
                    return False

        except Exception as e:
            logger.error(f"取消故障反馈失败: {e}")
            return False

    def perform_fault_feedback_process(
        self, fault_description: str, contact_info: str = "", submit: bool = True
    ) -> bool:
        """
        执行完整的故障反馈流程

        Args:
            fault_description (str): 故障描述
            contact_info (str): 联系信息（可选）
            submit (bool): 是否提交（True提交，False取消）

        Returns:
            bool: 操作是否成功
        """
        try:
            with allure.step("执行故障反馈流程"):
                # 1. 打开故障反馈对话框
                if not self.open_fault_feedback_dialog():
                    return False

                # 等待对话框加载
                time.sleep(1)

                # 2. 填写表单
                if not self.fill_fault_feedback_form(fault_description, contact_info):
                    return False

                # 3. 提交或取消
                if submit:
                    return self.submit_fault_feedback()
                else:
                    return self.cancel_fault_feedback()

        except Exception as e:
            logger.error(f"执行故障反馈流程失败: {e}")
            return False
